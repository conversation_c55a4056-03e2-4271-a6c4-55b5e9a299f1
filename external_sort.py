#!/usr/bin/env python3
"""
External Sort for Large Files

This script implements external sorting to sort very large files that don't fit in memory.
It divides the file into smaller chunks, sorts each chunk, and then merges them back together.

Usage:
    python external_sort.py <input_file> [output_file] [--chunk-size MB] [--sort-column N] [--reverse]
    
Example:
    python external_sort.py correl_1day.txt correl_1day_sorted.txt --chunk-size 100 --sort-column 3 --reverse
"""

import os
import sys
import heapq
import tempfile
import argparse
from typing import List, Iterator, Tuple
import csv

class ExternalSorter:
    def __init__(self, chunk_size_mb: int = 100, temp_dir: str = None):
        """
        Initialize the external sorter.
        
        Args:
            chunk_size_mb: Size of each chunk in MB
            temp_dir: Directory for temporary files (None for system temp)
        """
        self.chunk_size_bytes = chunk_size_mb * 1024 * 1024
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.temp_files = []
    
    def _get_sort_key(self, line: str, sort_column: int, reverse: bool = False) -> Tuple:
        """
        Extract sort key from a line.
        
        Args:
            line: Line from the file
            sort_column: Column number to sort by (1-based)
            reverse: Whether to reverse the sort order
            
        Returns:
            Tuple that can be used for sorting
        """
        try:
            parts = line.strip().split(',')
            if sort_column <= len(parts):
                # Try to convert to float for numeric sorting
                try:
                    value = float(parts[sort_column - 1])
                    return (value if not reverse else -value, line)
                except ValueError:
                    # Fall back to string sorting
                    value = parts[sort_column - 1]
                    return (value, line)
            else:
                return (line, line)  # Fallback to line sorting
        except:
            return (line, line)  # Fallback to line sorting
    
    def _split_and_sort_chunks(self, input_file: str, sort_column: int, reverse: bool = False) -> List[str]:
        """
        Split the input file into sorted chunks.
        
        Args:
            input_file: Path to input file
            sort_column: Column to sort by (1-based)
            reverse: Whether to reverse sort
            
        Returns:
            List of temporary file paths
        """
        print(f"Splitting file into chunks of {self.chunk_size_bytes // (1024*1024)} MB...")
        
        chunk_num = 0
        current_chunk = []
        current_size = 0
        
        with open(input_file, 'r', encoding='utf-8') as f:
            header = f.readline()  # Read header
            
            for line in f:
                line_size = len(line.encode('utf-8'))
                
                if current_size + line_size > self.chunk_size_bytes and current_chunk:
                    # Save current chunk
                    temp_file = self._save_sorted_chunk(current_chunk, chunk_num, header, sort_column, reverse)
                    self.temp_files.append(temp_file)
                    
                    # Start new chunk
                    current_chunk = [line]
                    current_size = line_size
                    chunk_num += 1
                    
                    if chunk_num % 10 == 0:
                        print(f"Processed {chunk_num} chunks...")
                else:
                    current_chunk.append(line)
                    current_size += line_size
            
            # Save final chunk
            if current_chunk:
                temp_file = self._save_sorted_chunk(current_chunk, chunk_num, header, sort_column, reverse)
                self.temp_files.append(temp_file)
        
        print(f"Created {len(self.temp_files)} sorted chunks")
        return self.temp_files
    
    def _save_sorted_chunk(self, chunk: List[str], chunk_num: int, header: str, 
                          sort_column: int, reverse: bool = False) -> str:
        """
        Sort and save a chunk to a temporary file.
        
        Args:
            chunk: List of lines to sort
            chunk_num: Chunk number for filename
            header: Header line to include
            sort_column: Column to sort by
            reverse: Whether to reverse sort
            
        Returns:
            Path to temporary file
        """
        # Sort the chunk
        chunk.sort(key=lambda line: self._get_sort_key(line, sort_column, reverse))
        
        # Save to temporary file
        temp_file = os.path.join(self.temp_dir, f"chunk_{chunk_num:06d}.tmp")
        
        with open(temp_file, 'w', encoding='utf-8') as f:
            if chunk_num == 0:  # Only write header in first chunk for reference
                f.write(header)
            for line in chunk:
                f.write(line)
        
        return temp_file
    
    def _merge_chunks(self, output_file: str, sort_column: int, reverse: bool = False):
        """
        Merge sorted chunks into final output file.
        
        Args:
            output_file: Path to output file
            sort_column: Column to sort by
            reverse: Whether to reverse sort
        """
        print(f"Merging {len(self.temp_files)} chunks into {output_file}...")
        
        # Open all chunk files
        chunk_files = []
        chunk_iterators = []
        
        try:
            for temp_file in self.temp_files:
                f = open(temp_file, 'r', encoding='utf-8')
                chunk_files.append(f)
                
                # Skip header if present (only in first chunk)
                first_line = f.readline()
                if first_line.startswith('Ticker1,Ticker2,Correlation'):
                    # This is a header, get the next line
                    first_line = f.readline()
                
                if first_line.strip():
                    chunk_iterators.append((self._get_sort_key(first_line, sort_column, reverse)[0], 
                                          first_line, f, len(chunk_iterators)))
            
            # Create heap for merging
            heapq.heapify(chunk_iterators)
            
            # Write merged output
            with open(output_file, 'w', encoding='utf-8') as out_f:
                # Write header
                out_f.write("Ticker1,Ticker2,Correlation\n")
                
                lines_written = 0
                while chunk_iterators:
                    # Get the smallest item
                    sort_key, line, file_handle, file_idx = heapq.heappop(chunk_iterators)
                    out_f.write(line)
                    lines_written += 1
                    
                    if lines_written % 100000 == 0:
                        print(f"Merged {lines_written:,} lines...")
                    
                    # Get next line from the same file
                    next_line = file_handle.readline()
                    if next_line.strip():
                        heapq.heappush(chunk_iterators, 
                                     (self._get_sort_key(next_line, sort_column, reverse)[0], 
                                      next_line, file_handle, file_idx))
                
                print(f"Merge complete! Total lines written: {lines_written:,}")
        
        finally:
            # Close all files
            for f in chunk_files:
                f.close()
    
    def sort_file(self, input_file: str, output_file: str, sort_column: int = 3, reverse: bool = False):
        """
        Sort a large file using external sorting.
        
        Args:
            input_file: Path to input file
            output_file: Path to output file
            sort_column: Column to sort by (1-based, default 3 for correlation)
            reverse: Whether to sort in descending order
        """
        try:
            print(f"Starting external sort of {input_file}")
            print(f"Output will be written to {output_file}")
            print(f"Sorting by column {sort_column}, reverse={reverse}")
            
            # Check input file exists
            if not os.path.exists(input_file):
                raise FileError(f"Input file not found: {input_file}")
            
            file_size = os.path.getsize(input_file)
            print(f"Input file size: {file_size / (1024*1024):.1f} MB")
            
            # Step 1: Split and sort chunks
            self._split_and_sort_chunks(input_file, sort_column, reverse)
            
            # Step 2: Merge chunks
            self._merge_chunks(output_file, sort_column, reverse)
            
            print("External sort completed successfully!")
            
        finally:
            # Clean up temporary files
            self._cleanup()
    
    def _cleanup(self):
        """Clean up temporary files."""
        print("Cleaning up temporary files...")
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                print(f"Warning: Could not remove {temp_file}: {e}")
        self.temp_files.clear()

def main():
    parser = argparse.ArgumentParser(description='External sort for large files')
    parser.add_argument('input_file', help='Input file to sort')
    parser.add_argument('output_file', nargs='?', help='Output file (default: input_file_sorted.txt)')
    parser.add_argument('--chunk-size', type=int, default=100, 
                       help='Chunk size in MB (default: 100)')
    parser.add_argument('--sort-column', type=int, default=3,
                       help='Column to sort by (1-based, default: 3)')
    parser.add_argument('--reverse', action='store_true',
                       help='Sort in descending order')
    
    args = parser.parse_args()
    
    # Set default output file if not provided
    if not args.output_file:
        base, ext = os.path.splitext(args.input_file)
        args.output_file = f"{base}_sorted{ext}"
    
    # Create sorter and run
    sorter = ExternalSorter(chunk_size_mb=args.chunk_size)
    sorter.sort_file(args.input_file, args.output_file, args.sort_column, args.reverse)

if __name__ == "__main__":
    main()
