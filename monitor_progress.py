#!/usr/bin/env python3
"""
Monitor Progress of Correlation Analysis

This script monitors the progress of the correlation analysis by checking the output file size
and estimating completion time.
"""

import os
import time
import sys
from datetime import datetime, timedelta

def monitor_correlation_progress(output_file="correl_1day.txt", total_pairs=7606950):
    """
    Monitor the progress of correlation analysis.
    
    Args:
        output_file: Path to the correlation output file
        total_pairs: Total number of pairs to process
    """
    print(f"Monitoring progress of {output_file}")
    print(f"Total pairs to process: {total_pairs:,}")
    print("Press Ctrl+C to stop monitoring\n")
    
    start_time = time.time()
    last_size = 0
    last_time = start_time
    
    try:
        while True:
            if os.path.exists(output_file):
                # Count lines in file (subtract 1 for header)
                try:
                    with open(output_file, 'r') as f:
                        lines = sum(1 for _ in f) - 1  # Subtract header
                    
                    current_time = time.time()
                    elapsed = current_time - start_time
                    
                    if lines > 0:
                        progress_pct = (lines / total_pairs) * 100
                        
                        # Calculate rate
                        if elapsed > 0:
                            rate = lines / elapsed  # pairs per second
                            
                            # Estimate remaining time
                            remaining_pairs = total_pairs - lines
                            if rate > 0:
                                eta_seconds = remaining_pairs / rate
                                eta = datetime.now() + timedelta(seconds=eta_seconds)
                                eta_str = eta.strftime("%Y-%m-%d %H:%M:%S")
                            else:
                                eta_str = "Unknown"
                        else:
                            rate = 0
                            eta_str = "Calculating..."
                        
                        # File size
                        file_size_mb = os.path.getsize(output_file) / (1024 * 1024)
                        
                        print(f"\r{datetime.now().strftime('%H:%M:%S')} | "
                              f"Progress: {lines:,}/{total_pairs:,} ({progress_pct:.2f}%) | "
                              f"Rate: {rate:.1f} pairs/sec | "
                              f"Size: {file_size_mb:.1f} MB | "
                              f"ETA: {eta_str}", end="", flush=True)
                    else:
                        print(f"\r{datetime.now().strftime('%H:%M:%S')} | "
                              f"File exists but no data yet...", end="", flush=True)
                        
                except Exception as e:
                    print(f"\rError reading file: {e}", end="", flush=True)
            else:
                print(f"\r{datetime.now().strftime('%H:%M:%S')} | "
                      f"Waiting for output file to be created...", end="", flush=True)
            
            time.sleep(30)  # Update every 30 seconds
            
    except KeyboardInterrupt:
        print(f"\n\nMonitoring stopped.")
        if os.path.exists(output_file):
            try:
                with open(output_file, 'r') as f:
                    final_lines = sum(1 for _ in f) - 1
                final_progress = (final_lines / total_pairs) * 100
                file_size_mb = os.path.getsize(output_file) / (1024 * 1024)
                print(f"Final status: {final_lines:,} pairs processed ({final_progress:.2f}%)")
                print(f"File size: {file_size_mb:.1f} MB")
            except:
                print("Could not read final status")

def main():
    if len(sys.argv) > 1:
        output_file = sys.argv[1]
    else:
        output_file = "correl_1day.txt"
    
    if len(sys.argv) > 2:
        total_pairs = int(sys.argv[2])
    else:
        total_pairs = 7606950
    
    monitor_correlation_progress(output_file, total_pairs)

if __name__ == "__main__":
    main()
