#!/usr/bin/env python3
"""
Ticker Correlation Analyzer

This script analyzes correlations between ticker pairs for a given time period.
It reads financial data files in the format {ticker}_{time_period}_adjsplit.txt or {ticker}_{time_period}.txt
and calculates correlations between all unique ticker pairs.

Usage:
    python ticker_correlation_analyzer.py <time_period>
    
Example:
    python ticker_correlation_analyzer.py 1day
"""

import os
import sys
import pandas as pd
import numpy as np
from itertools import combinations
import glob
import re
from typing import List, Tuple, Dict

def get_unique_tickers(time_period: str, data_dir: str = "securities") -> List[str]:
    """
    Extract unique tickers for a given time period from filenames.

    Args:
        time_period: Time period (e.g., '1day', '1min', '30min', '5min')
        data_dir: Directory containing the data files

    Returns:
        List of unique ticker symbols
    """
    # Pattern to match files with the specified time period
    pattern1 = f"*_full_{time_period}_adjsplit.txt"
    pattern2 = f"*_full_{time_period}.txt"
    pattern3 = f"*_full_{time_period}_continuous_ratio_adjusted.txt"

    files1 = glob.glob(os.path.join(data_dir, pattern1))
    files2 = glob.glob(os.path.join(data_dir, pattern2))
    files3 = glob.glob(os.path.join(data_dir, pattern3))

    all_files = files1 + files2 + files3
  
    tickers = []
    for file_path in all_files:
        filename = os.path.basename(file_path)
        # Extract ticker from filename
        # Pattern: {ticker}_full_{time_period}_adjsplit.txt, {ticker}_full_{time_period}.txt, or {ticker}_full_{time_period}_continuous_ratio_adjusted.txt
        match = re.match(r'^([^_]+)_full_' + re.escape(time_period) + r'(_adjsplit|_continuous_ratio_adjusted)?\.txt$', filename)
        if match:
            ticker = match.group(1)
            tickers.append(ticker)
    
    return sorted(list(set(tickers)))

def load_ticker_data(ticker: str, time_period: str, data_dir: str = "securities") -> pd.DataFrame:
    """
    Load data for a specific ticker and time period.
    
    Args:
        ticker: Ticker symbol
        time_period: Time period
        data_dir: Directory containing the data files
        
    Returns:
        DataFrame with ticker data or None if file not found
    """
    # Try all possible filename formats
    file_patterns = [
        f"{ticker}_full_{time_period}_continuous_ratio_adjusted.txt",
        f"{ticker}_full_{time_period}_adjsplit.txt",
        f"{ticker}_full_{time_period}.txt"
    ]
    
    for pattern in file_patterns:
        file_path = os.path.join(data_dir, pattern)
        if os.path.exists(file_path):
            try:
                # First, read the file to determine the number of columns
                with open(file_path, 'r') as f:
                    first_line = f.readline().strip()
                    num_columns = len(first_line.split(','))

                # Set column names based on file type
                if num_columns == 7:
                    # Continuous ratio adjusted files have 7 columns
                    column_names = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'OpenInterest']
                else:
                    # Regular files have 6 columns
                    column_names = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']

                # Read the CSV file with appropriate column names
                df = pd.read_csv(file_path, header=None, names=column_names)
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
                return df
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue
    
    return None

def calculate_correlation(ticker1_data: pd.DataFrame, ticker2_data: pd.DataFrame) -> float:
    """
    Calculate rolling window correlation between two ticker datasets using closing prices.
    Uses a 30-period rolling window and applies strict filtering criteria:
    - Standard deviation of rolling correlations >= 0.4
    - Mean rolling correlation between 0.7-0.8 or -0.7 to -0.8

    Args:
        ticker1_data: DataFrame for first ticker
        ticker2_data: DataFrame for second ticker

    Returns:
        Average rolling correlation coefficient or NaN if criteria not met
    """
    try:
        # Align the data by date (inner join to get common dates)
        merged = pd.merge(ticker1_data[['Close']], ticker2_data[['Close']],
                         left_index=True, right_index=True, how='inner',
                         suffixes=('_1', '_2'))

        # Require at least 60 overlapping data points for meaningful rolling correlation
        # (30 for window + 30 additional for stable average)
        min_overlap = 60
        if len(merged) < min_overlap:
            return np.nan

        # Check for valid price data (no NaN, no zeros, some variance)
        if (merged['Close_1'].isna().any() or merged['Close_2'].isna().any() or
            (merged['Close_1'] == 0).any() or (merged['Close_2'] == 0).any() or
            merged['Close_1'].std() == 0 or merged['Close_2'].std() == 0):
            return np.nan

        # Sort by date to ensure proper chronological order
        merged = merged.sort_index()

        # Calculate 30-period rolling correlation
        rolling_window = 30
        rolling_corr = merged['Close_1'].rolling(window=rolling_window).corr(merged['Close_2'])

        # Remove NaN values from rolling correlation
        rolling_corr = rolling_corr.dropna()

        if len(rolling_corr) == 0:
            return np.nan

        # Calculate statistics of rolling correlations
        avg_rolling_corr = rolling_corr.mean()
        std_rolling_corr = rolling_corr.std()

        # Apply filtering criteria (adjusted based on observed data):
        # 1. Standard deviation >= 0.4 (was 1.0, but typical values are 0.4-0.5)
        # 2. Mean between 0.7-0.8 or -0.7 to -0.8 (keeping original requirement)

        # Debug: Print first few pairs to understand the data distribution
        if not hasattr(calculate_correlation, 'debug_count'):
            calculate_correlation.debug_count = 0
        calculate_correlation.debug_count += 1
        if calculate_correlation.debug_count <= 5:  # Print first 5 pairs for debugging
            print(f"Debug pair {calculate_correlation.debug_count} - Rolling corr stats: mean={avg_rolling_corr:.3f}, std={std_rolling_corr:.3f}, rolling_corr_count={len(rolling_corr)}")

        if (std_rolling_corr >= 0.4 and
            ((0.7 <= avg_rolling_corr <= 0.8) or (-0.8 <= avg_rolling_corr <= -0.7))):
            return avg_rolling_corr
        else:
            return np.nan  # Discard pairs that don't meet criteria

    except Exception as e:
        print(f"Error calculating rolling correlation: {e}")
        return np.nan

def generate_pairs_file(time_period: str, data_dir: str = "securities", max_tickers: int = None) -> str:
    """
    Generate a file containing all ticker pairs for the given time period.

    Args:
        time_period: Time period to analyze
        data_dir: Directory containing the data files
        max_tickers: Maximum number of tickers to process (None for all)

    Returns:
        Path to the generated pairs file
    """
    print(f"Getting unique tickers for time period: {time_period}")
    tickers = get_unique_tickers(time_period, data_dir)
    print(f"Found {len(tickers)} unique tickers")

    # Limit tickers if specified
    if max_tickers and len(tickers) > max_tickers:
        tickers = tickers[:max_tickers]
        print(f"Limited to first {len(tickers)} tickers")

    if len(tickers) < 2:
        print("Need at least 2 tickers to calculate correlations")
        return None

    # Filter tickers to only include those with valid data
    print("Validating ticker data availability...")
    valid_tickers = []
    for ticker in tickers:
        data = load_ticker_data(ticker, time_period, data_dir)
        if data is not None and not data.empty:
            valid_tickers.append(ticker)
        else:
            print(f"Warning: Could not load data for ticker {ticker}")

    print(f"Found {len(valid_tickers)} tickers with valid data")

    if len(valid_tickers) < 2:
        print("Need at least 2 tickers with valid data to calculate correlations")
        return None

    # Generate pairs file
    pairs_file = f"pairs_{time_period}.txt"
    total_pairs = len(list(combinations(valid_tickers, 2)))

    print(f"Generating {total_pairs} ticker pairs...")
    print(f"Writing pairs to {pairs_file}")

    with open(pairs_file, 'w') as f:
        for ticker1, ticker2 in combinations(valid_tickers, 2):
            f.write(f"{ticker1},{ticker2}\n")

    print(f"Pairs file generated: {pairs_file}")
    return pairs_file

def process_correlations_from_pairs_file(pairs_file: str, time_period: str, data_dir: str = "securities"):
    """
    Process correlations by reading pairs from file row by row and writing results to output file.

    Args:
        pairs_file: Path to the file containing ticker pairs
        time_period: Time period being analyzed
        data_dir: Directory containing the data files
    """
    output_file = f"correl_{time_period}.txt"

    print(f"Processing correlations from {pairs_file}")
    print(f"Writing results to {output_file}")

    # Count total pairs for progress tracking
    with open(pairs_file, 'r') as f:
        total_pairs = sum(1 for line in f)

    print(f"Total pairs to process: {total_pairs}")

    # Cache for loaded ticker data to avoid reloading
    ticker_data_cache = {}

    def get_ticker_data(ticker: str):
        if ticker not in ticker_data_cache:
            ticker_data_cache[ticker] = load_ticker_data(ticker, time_period, data_dir)
        return ticker_data_cache[ticker]

    # Process pairs and write results
    pairs_meeting_criteria = 0
    with open(pairs_file, 'r') as pairs_f, open(output_file, 'w') as output_f:
        # Write header
        output_f.write("Ticker1,Ticker2,AvgRollingCorrelation\n")

        for i, line in enumerate(pairs_f):
            if i % 1000 == 0:  # Progress indicator every 1000 pairs
                print(f"Progress: {i}/{total_pairs} pairs processed ({i/total_pairs*100:.1f}%) - Pairs meeting criteria: {pairs_meeting_criteria}")

            line = line.strip()
            if not line:
                continue

            try:
                ticker1, ticker2 = line.split(',')

                # Load data for both tickers
                data1 = get_ticker_data(ticker1)
                data2 = get_ticker_data(ticker2)

                if data1 is None or data2 is None:
                    correlation = np.nan
                else:
                    correlation = calculate_correlation(data1, data2)

                # Only write pairs that meet the criteria (correlation is not NaN)
                # The calculate_correlation function filters for std >= 0.4 and mean in [0.7-0.8] or [-0.8--0.7]
                if not np.isnan(correlation):
                    output_f.write(f"{ticker1},{ticker2},{correlation:.6f}\n")
                    pairs_meeting_criteria += 1

            except Exception as e:
                print(f"Error processing pair {line}: {e}")
                continue

    print(f"Correlation processing complete!")
    print(f"Total pairs processed: {total_pairs}")
    print(f"Pairs meeting criteria (std >= 0.4, mean in [0.7-0.8] or [-0.8--0.7]): {pairs_meeting_criteria}")
    print(f"Percentage meeting criteria: {pairs_meeting_criteria/total_pairs*100:.2f}%")
    print(f"Results written to {output_file}")

    # Clean up pairs file
    try:
        os.remove(pairs_file)
        print(f"Cleaned up temporary pairs file: {pairs_file}")
    except:
        print(f"Could not remove pairs file: {pairs_file}")

def analyze_correlations(time_period: str, data_dir: str = "securities", max_tickers: int = None):
    """
    Analyze rolling window correlations between all ticker pairs for a given time period.
    Uses a two-phase approach: generate pairs file, then process correlations.
    Only keeps pairs where:
    - Standard deviation of 30-period rolling correlations >= 0.4
    - Mean rolling correlation between 0.7-0.8 or -0.7 to -0.8

    Args:
        time_period: Time period to analyze
        data_dir: Directory containing the data files
        max_tickers: Maximum number of tickers to process (None for all)
    """
    # Phase 1: Generate pairs file
    pairs_file = generate_pairs_file(time_period, data_dir, max_tickers)

    if not pairs_file:
        print("Failed to generate pairs file")
        return

    # Phase 2: Process correlations from pairs file
    process_correlations_from_pairs_file(pairs_file, time_period, data_dir)

def print_correlation_statistics(output_file: str):
    """
    Read correlation results file and print statistics.

    Args:
        output_file: Path to the correlation results file
    """
    try:
        print(f"\nReading statistics from {output_file}")
        correlations = []

        with open(output_file, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split(',')
                if len(parts) == 3:
                    try:
                        corr = float(parts[2])
                        if not np.isnan(corr):
                            correlations.append(corr)
                    except ValueError:
                        continue

        if correlations:
            print(f"\nStatistics:")
            print(f"Valid correlations: {len(correlations)}")
            print(f"Average correlation: {np.mean(correlations):.4f}")
            print(f"Max correlation: {np.max(correlations):.4f}")
            print(f"Min correlation: {np.min(correlations):.4f}")
            print(f"Std deviation: {np.std(correlations):.4f}")
        else:
            print("No valid correlations found")

    except Exception as e:
        print(f"Error reading statistics: {e}")

def run_analysis_for_time_period(time_period: str, max_tickers: int = None):
    """Run correlation analysis for a single time period."""
    print(f"\n{'='*60}")
    print(f"Starting correlation analysis for time period: {time_period}")
    if max_tickers:
        print(f"Limiting analysis to first {max_tickers} tickers")
    print(f"{'='*60}")

    # Analyze correlations using two-phase approach
    analyze_correlations(time_period, max_tickers=max_tickers)

    # Print statistics and sort results
    output_file = f"correl_{time_period}.txt"
    if os.path.exists(output_file):
        print_correlation_statistics(output_file)

        # Automatically sort the results
        sorted_file = f"correl_{time_period}_sorted.txt"
        print(f"\nSorting correlation results...")
        try:
            import subprocess
            result = subprocess.run([
                'python', 'external_sort.py', output_file, sorted_file,
                '--chunk-size', '100', '--sort-column', '3', '--reverse'
            ], capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                print(f"Results sorted and saved to {sorted_file}")
            else:
                print(f"Sorting failed: {result.stderr}")
        except Exception as e:
            print(f"Could not run external sort: {e}")
            print("You can manually sort using:")
            print(f"python external_sort.py {output_file} {sorted_file} --chunk-size 100 --sort-column 3 --reverse")

    print(f"Analysis complete for {time_period}!")

def main():
    """Main function to run the correlation analysis."""
    if len(sys.argv) < 2:
        print("Usage: python ticker_correlation_analyzer.py <time_period1> [time_period2] ... [max_tickers]")
        print("Examples:")
        print("  python ticker_correlation_analyzer.py 1day")
        print("  python ticker_correlation_analyzer.py 1day 30min")
        print("  python ticker_correlation_analyzer.py 1day 30min 5min")
        print("  python ticker_correlation_analyzer.py 1day 100  # limit to 100 tickers")
        print("  python ticker_correlation_analyzer.py 1day 30min 100  # multiple periods with limit")
        print("\nSupported time periods: 1day, 30min, 5min, 1min")
        print("max_tickers: Optional limit on number of tickers to process (default: all)")
        sys.exit(1)

    # Parse arguments
    args = sys.argv[1:]
    time_periods = []
    max_tickers = None

    # Check if last argument is a number (max_tickers)
    if args and args[-1].isdigit():
        max_tickers = int(args[-1])
        if max_tickers <= 0:
            print("Error: max_tickers must be a positive integer")
            sys.exit(1)
        args = args[:-1]  # Remove max_tickers from time periods

    # Validate time periods
    valid_periods = ['1day', '30min', '5min', '1min']
    for period in args:
        if period not in valid_periods:
            print(f"Error: '{period}' is not a valid time period")
            print(f"Valid time periods: {', '.join(valid_periods)}")
            sys.exit(1)
        time_periods.append(period)

    if not time_periods:
        print("Error: At least one time period must be specified")
        sys.exit(1)

    print(f"Will analyze {len(time_periods)} time period(s): {', '.join(time_periods)}")
    if max_tickers:
        print(f"Limiting analysis to first {max_tickers} tickers for each period")

    # Run analysis for each time period
    for i, time_period in enumerate(time_periods, 1):
        print(f"\n[{i}/{len(time_periods)}] Processing time period: {time_period}")
        try:
            run_analysis_for_time_period(time_period, max_tickers)
        except KeyboardInterrupt:
            print(f"\nAnalysis interrupted for {time_period}")
            break
        except Exception as e:
            print(f"Error analyzing {time_period}: {e}")
            continue

    print(f"\n{'='*60}")
    print("All correlation analyses complete!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
