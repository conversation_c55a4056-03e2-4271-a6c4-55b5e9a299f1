#!/usr/bin/env python3
"""
Ticker Correlation Analyzer

This script analyzes correlations between ticker pairs for a given time period.
It reads financial data files in the format {ticker}_{time_period}_adjsplit.txt or {ticker}_{time_period}.txt
and calculates correlations between all unique ticker pairs.

Usage:
    python ticker_correlation_analyzer.py <time_period>
    
Example:
    python ticker_correlation_analyzer.py 1day
"""

import os
import sys
import pandas as pd
import numpy as np
from itertools import combinations
import glob
import re
from typing import List, Tuple, Dict

def get_unique_tickers(time_period: str, data_dir: str = ".") -> List[str]:
    """
    Extract unique tickers for a given time period from filenames.
    
    Args:
        time_period: Time period (e.g., '1day', '1min', '30min', '5min')
        data_dir: Directory containing the data files
        
    Returns:
        List of unique ticker symbols
    """
    # Pattern to match files with the specified time period
    pattern1 = f"*_full_{time_period}_adjsplit.txt"
    pattern2 = f"*_full_{time_period}.txt"
    
    files1 = glob.glob(os.path.join(data_dir, pattern1))
    files2 = glob.glob(os.path.join(data_dir, pattern2))
    
    all_files = files1 + files2
    
    tickers = []
    for file_path in all_files:
        filename = os.path.basename(file_path)
        # Extract ticker from filename
        # Pattern: {ticker}_full_{time_period}_adjsplit.txt or {ticker}_full_{time_period}.txt
        match = re.match(r'^([^_]+)_full_' + re.escape(time_period) + r'(_adjsplit)?\.txt$', filename)
        if match:
            ticker = match.group(1)
            tickers.append(ticker)
    
    return sorted(list(set(tickers)))

def load_ticker_data(ticker: str, time_period: str, data_dir: str = ".") -> pd.DataFrame:
    """
    Load data for a specific ticker and time period.
    
    Args:
        ticker: Ticker symbol
        time_period: Time period
        data_dir: Directory containing the data files
        
    Returns:
        DataFrame with ticker data or None if file not found
    """
    # Try both possible filename formats
    file_patterns = [
        f"{ticker}_full_{time_period}_adjsplit.txt",
        f"{ticker}_full_{time_period}.txt"
    ]
    
    for pattern in file_patterns:
        file_path = os.path.join(data_dir, pattern)
        if os.path.exists(file_path):
            try:
                # Read the CSV file with appropriate column names
                df = pd.read_csv(file_path, header=None, 
                               names=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
                return df
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue
    
    return None

def calculate_correlation(ticker1_data: pd.DataFrame, ticker2_data: pd.DataFrame) -> float:
    """
    Calculate correlation between two ticker datasets using closing prices.
    
    Args:
        ticker1_data: DataFrame for first ticker
        ticker2_data: DataFrame for second ticker
        
    Returns:
        Correlation coefficient or NaN if calculation fails
    """
    try:
        # Align the data by date (inner join to get common dates)
        merged = pd.merge(ticker1_data[['Close']], ticker2_data[['Close']], 
                         left_index=True, right_index=True, how='inner', 
                         suffixes=('_1', '_2'))
        
        if len(merged) < 2:  # Need at least 2 data points for correlation
            return np.nan
            
        correlation = merged['Close_1'].corr(merged['Close_2'])
        return correlation
        
    except Exception as e:
        print(f"Error calculating correlation: {e}")
        return np.nan

def analyze_correlations(time_period: str, data_dir: str = ".", max_tickers: int = None) -> List[Tuple[str, str, float]]:
    """
    Analyze correlations between all ticker pairs for a given time period.

    Args:
        time_period: Time period to analyze
        data_dir: Directory containing the data files
        max_tickers: Maximum number of tickers to process (None for all)

    Returns:
        List of tuples (ticker1, ticker2, correlation)
    """
    print(f"Getting unique tickers for time period: {time_period}")
    tickers = get_unique_tickers(time_period, data_dir)
    print(f"Found {len(tickers)} unique tickers")

    # Limit tickers if specified
    if max_tickers and len(tickers) > max_tickers:
        tickers = tickers[:max_tickers]
        print(f"Limited to first {len(tickers)} tickers")

    if len(tickers) < 2:
        print("Need at least 2 tickers to calculate correlations")
        return []

    # Load all ticker data
    print("Loading ticker data...")
    ticker_data = {}
    for ticker in tickers:
        data = load_ticker_data(ticker, time_period, data_dir)
        if data is not None and not data.empty:
            ticker_data[ticker] = data
        else:
            print(f"Warning: Could not load data for ticker {ticker}")

    print(f"Successfully loaded data for {len(ticker_data)} tickers")

    # Calculate correlations for all pairs
    correlations = []
    ticker_list = list(ticker_data.keys())
    total_pairs = len(list(combinations(ticker_list, 2)))

    print(f"Calculating correlations for {total_pairs} ticker pairs...")

    for i, (ticker1, ticker2) in enumerate(combinations(ticker_list, 2)):
        if i % 1000 == 0:  # Progress indicator every 1000 pairs
            print(f"Progress: {i}/{total_pairs} pairs processed ({i/total_pairs*100:.1f}%)")

        correlation = calculate_correlation(ticker_data[ticker1], ticker_data[ticker2])
        correlations.append((ticker1, ticker2, correlation))

    print(f"Completed correlation analysis for {len(correlations)} pairs")
    return correlations

def write_correlation_results(correlations: List[Tuple[str, str, float]], 
                            time_period: str, output_dir: str = "."):
    """
    Write correlation results to a file.
    
    Args:
        correlations: List of correlation tuples
        time_period: Time period analyzed
        output_dir: Directory to write output file
    """
    output_file = os.path.join(output_dir, f"correl_{time_period}.txt")
    
    print(f"Writing results to {output_file}")
    
    with open(output_file, 'w') as f:
        f.write("Ticker1,Ticker2,Correlation\n")
        for ticker1, ticker2, corr in correlations:
            f.write(f"{ticker1},{ticker2},{corr:.6f}\n")
    
    print(f"Results written to {output_file}")
    
    # Print some statistics
    valid_correlations = [corr for _, _, corr in correlations if not np.isnan(corr)]
    if valid_correlations:
        print(f"\nStatistics:")
        print(f"Total pairs: {len(correlations)}")
        print(f"Valid correlations: {len(valid_correlations)}")
        print(f"Average correlation: {np.mean(valid_correlations):.4f}")
        print(f"Max correlation: {np.max(valid_correlations):.4f}")
        print(f"Min correlation: {np.min(valid_correlations):.4f}")

def main():
    """Main function to run the correlation analysis."""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python ticker_correlation_analyzer.py <time_period> [max_tickers]")
        print("Example: python ticker_correlation_analyzer.py 1day")
        print("Example: python ticker_correlation_analyzer.py 1day 100")
        print("  max_tickers: Optional limit on number of tickers to process (default: all)")
        sys.exit(1)

    time_period = sys.argv[1]
    max_tickers = None

    if len(sys.argv) == 3:
        try:
            max_tickers = int(sys.argv[2])
            if max_tickers <= 0:
                print("Error: max_tickers must be a positive integer")
                sys.exit(1)
        except ValueError:
            print("Error: max_tickers must be a valid integer")
            sys.exit(1)

    print(f"Starting correlation analysis for time period: {time_period}")
    if max_tickers:
        print(f"Limiting analysis to first {max_tickers} tickers")

    # Analyze correlations
    correlations = analyze_correlations(time_period, max_tickers=max_tickers)

    if not correlations:
        print("No correlations calculated. Exiting.")
        sys.exit(1)

    # Write results
    write_correlation_results(correlations, time_period)

    print("Analysis complete!")

if __name__ == "__main__":
    main()
