#!/usr/bin/env python3
"""
Ticker Correlation Analyzer

This script analyzes correlations between ticker pairs for a given time period.
It reads financial data files in the format {ticker}_{time_period}_adjsplit.txt or {ticker}_{time_period}.txt
and calculates correlations between all unique ticker pairs.

Usage:
    python ticker_correlation_analyzer.py <time_period>
    
Example:
    python ticker_correlation_analyzer.py 1day
"""

import os
import sys
import pandas as pd
import numpy as np
from itertools import combinations
import glob
import re
from typing import List, Tuple, Dict

def get_unique_tickers(time_period: str, data_dir: str = "securities") -> List[str]:
    """
    Extract unique tickers for a given time period from filenames.

    Args:
        time_period: Time period (e.g., '1day', '1min', '30min', '5min')
        data_dir: Directory containing the data files

    Returns:
        List of unique ticker symbols
    """
    # Pattern to match files with the specified time period
    pattern1 = f"*_full_{time_period}_adjsplit.txt"
    pattern2 = f"*_full_{time_period}.txt"

    files1 = glob.glob(os.path.join(data_dir, pattern1))
    files2 = glob.glob(os.path.join(data_dir, pattern2))

    all_files = files1 + files2
  
    tickers = []
    for file_path in all_files:
        filename = os.path.basename(file_path)
        # Extract ticker from filename
        # Pattern: {ticker}_full_{time_period}_adjsplit.txt or {ticker}_full_{time_period}.txt
        match = re.match(r'^([^_]+)_full_' + re.escape(time_period) + r'(_adjsplit)?\.txt$', filename)
        if match:
            ticker = match.group(1)
            tickers.append(ticker)
    
    return sorted(list(set(tickers)))

def load_ticker_data(ticker: str, time_period: str, data_dir: str = "securities") -> pd.DataFrame:
    """
    Load data for a specific ticker and time period.
    
    Args:
        ticker: Ticker symbol
        time_period: Time period
        data_dir: Directory containing the data files
        
    Returns:
        DataFrame with ticker data or None if file not found
    """
    # Try both possible filename formats
    file_patterns = [
        f"{ticker}_full_{time_period}_adjsplit.txt",
        f"{ticker}_full_{time_period}.txt"
    ]
    
    for pattern in file_patterns:
        file_path = os.path.join(data_dir, pattern)
        if os.path.exists(file_path):
            try:
                # Read the CSV file with appropriate column names
                df = pd.read_csv(file_path, header=None, 
                               names=['Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
                df['Date'] = pd.to_datetime(df['Date'])
                df.set_index('Date', inplace=True)
                return df
            except Exception as e:
                print(f"Error reading {file_path}: {e}")
                continue
    
    return None

def calculate_correlation(ticker1_data: pd.DataFrame, ticker2_data: pd.DataFrame) -> float:
    """
    Calculate correlation between two ticker datasets using closing prices.
    Ensures proper timestamp alignment and requires minimum overlap.

    Args:
        ticker1_data: DataFrame for first ticker
        ticker2_data: DataFrame for second ticker

    Returns:
        Correlation coefficient or NaN if calculation fails
    """
    try:
        # Align the data by date (inner join to get common dates)
        merged = pd.merge(ticker1_data[['Close']], ticker2_data[['Close']],
                         left_index=True, right_index=True, how='inner',
                         suffixes=('_1', '_2'))

        # Require at least 30 overlapping data points for meaningful correlation
        min_overlap = 30
        if len(merged) < min_overlap:
            return np.nan

        # Check for valid price data (no NaN, no zeros, some variance)
        if (merged['Close_1'].isna().any() or merged['Close_2'].isna().any() or
            (merged['Close_1'] == 0).any() or (merged['Close_2'] == 0).any() or
            merged['Close_1'].std() == 0 or merged['Close_2'].std() == 0):
            return np.nan

        correlation = merged['Close_1'].corr(merged['Close_2'])

        # Ensure correlation is valid
        if pd.isna(correlation):
            return np.nan

        return correlation

    except Exception as e:
        print(f"Error calculating correlation: {e}")
        return np.nan

def generate_pairs_file(time_period: str, data_dir: str = "securities", max_tickers: int = None) -> str:
    """
    Generate a file containing all ticker pairs for the given time period.

    Args:
        time_period: Time period to analyze
        data_dir: Directory containing the data files
        max_tickers: Maximum number of tickers to process (None for all)

    Returns:
        Path to the generated pairs file
    """
    print(f"Getting unique tickers for time period: {time_period}")
    tickers = get_unique_tickers(time_period, data_dir)
    print(f"Found {len(tickers)} unique tickers")

    # Limit tickers if specified
    if max_tickers and len(tickers) > max_tickers:
        tickers = tickers[:max_tickers]
        print(f"Limited to first {len(tickers)} tickers")

    if len(tickers) < 2:
        print("Need at least 2 tickers to calculate correlations")
        return None

    # Filter tickers to only include those with valid data
    print("Validating ticker data availability...")
    valid_tickers = []
    for ticker in tickers:
        data = load_ticker_data(ticker, time_period, data_dir)
        if data is not None and not data.empty:
            valid_tickers.append(ticker)
        else:
            print(f"Warning: Could not load data for ticker {ticker}")

    print(f"Found {len(valid_tickers)} tickers with valid data")

    if len(valid_tickers) < 2:
        print("Need at least 2 tickers with valid data to calculate correlations")
        return None

    # Generate pairs file
    pairs_file = f"pairs_{time_period}.txt"
    total_pairs = len(list(combinations(valid_tickers, 2)))

    print(f"Generating {total_pairs} ticker pairs...")
    print(f"Writing pairs to {pairs_file}")

    with open(pairs_file, 'w') as f:
        for ticker1, ticker2 in combinations(valid_tickers, 2):
            f.write(f"{ticker1},{ticker2}\n")

    print(f"Pairs file generated: {pairs_file}")
    return pairs_file

def process_correlations_from_pairs_file(pairs_file: str, time_period: str, data_dir: str = "securities"):
    """
    Process correlations by reading pairs from file row by row and writing results to output file.

    Args:
        pairs_file: Path to the file containing ticker pairs
        time_period: Time period being analyzed
        data_dir: Directory containing the data files
    """
    output_file = f"correl_{time_period}.txt"

    print(f"Processing correlations from {pairs_file}")
    print(f"Writing results to {output_file}")

    # Count total pairs for progress tracking
    with open(pairs_file, 'r') as f:
        total_pairs = sum(1 for line in f)

    print(f"Total pairs to process: {total_pairs}")

    # Cache for loaded ticker data to avoid reloading
    ticker_data_cache = {}

    def get_ticker_data(ticker: str):
        if ticker not in ticker_data_cache:
            ticker_data_cache[ticker] = load_ticker_data(ticker, time_period, data_dir)
        return ticker_data_cache[ticker]

    # Process pairs and write results
    with open(pairs_file, 'r') as pairs_f, open(output_file, 'w') as output_f:
        # Write header
        output_f.write("Ticker1,Ticker2,Correlation\n")

        for i, line in enumerate(pairs_f):
            if i % 1000 == 0:  # Progress indicator every 1000 pairs
                print(f"Progress: {i}/{total_pairs} pairs processed ({i/total_pairs*100:.1f}%)")

            line = line.strip()
            if not line:
                continue

            try:
                ticker1, ticker2 = line.split(',')

                # Load data for both tickers
                data1 = get_ticker_data(ticker1)
                data2 = get_ticker_data(ticker2)

                if data1 is None or data2 is None:
                    correlation = np.nan
                else:
                    correlation = calculate_correlation(data1, data2)

                # Write result immediately
                output_f.write(f"{ticker1},{ticker2},{correlation:.6f}\n")

            except Exception as e:
                print(f"Error processing pair {line}: {e}")
                continue

    print(f"Correlation processing complete!")
    print(f"Results written to {output_file}")

    # Clean up pairs file
    try:
        os.remove(pairs_file)
        print(f"Cleaned up temporary pairs file: {pairs_file}")
    except:
        print(f"Could not remove pairs file: {pairs_file}")

def analyze_correlations(time_period: str, data_dir: str = "securities", max_tickers: int = None):
    """
    Analyze correlations between all ticker pairs for a given time period.
    Uses a two-phase approach: generate pairs file, then process correlations.

    Args:
        time_period: Time period to analyze
        data_dir: Directory containing the data files
        max_tickers: Maximum number of tickers to process (None for all)
    """
    # Phase 1: Generate pairs file
    pairs_file = generate_pairs_file(time_period, data_dir, max_tickers)

    if not pairs_file:
        print("Failed to generate pairs file")
        return

    # Phase 2: Process correlations from pairs file
    process_correlations_from_pairs_file(pairs_file, time_period, data_dir)

def print_correlation_statistics(output_file: str):
    """
    Read correlation results file and print statistics.

    Args:
        output_file: Path to the correlation results file
    """
    try:
        print(f"\nReading statistics from {output_file}")
        correlations = []

        with open(output_file, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split(',')
                if len(parts) == 3:
                    try:
                        corr = float(parts[2])
                        if not np.isnan(corr):
                            correlations.append(corr)
                    except ValueError:
                        continue

        if correlations:
            print(f"\nStatistics:")
            print(f"Valid correlations: {len(correlations)}")
            print(f"Average correlation: {np.mean(correlations):.4f}")
            print(f"Max correlation: {np.max(correlations):.4f}")
            print(f"Min correlation: {np.min(correlations):.4f}")
            print(f"Std deviation: {np.std(correlations):.4f}")
        else:
            print("No valid correlations found")

    except Exception as e:
        print(f"Error reading statistics: {e}")

def main():
    """Main function to run the correlation analysis."""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python ticker_correlation_analyzer.py <time_period> [max_tickers]")
        print("Example: python ticker_correlation_analyzer.py 1day")
        print("Example: python ticker_correlation_analyzer.py 1day 100")
        print("  max_tickers: Optional limit on number of tickers to process (default: all)")
        sys.exit(1)

    time_period = sys.argv[1]
    max_tickers = None

    if len(sys.argv) == 3:
        try:
            max_tickers = int(sys.argv[2])
            if max_tickers <= 0:
                print("Error: max_tickers must be a positive integer")
                sys.exit(1)
        except ValueError:
            print("Error: max_tickers must be a valid integer")
            sys.exit(1)

    print(f"Starting correlation analysis for time period: {time_period}")
    if max_tickers:
        print(f"Limiting analysis to first {max_tickers} tickers")

    # Analyze correlations using two-phase approach
    analyze_correlations(time_period, max_tickers=max_tickers)

    # Print statistics and sort results
    output_file = f"correl_{time_period}.txt"
    if os.path.exists(output_file):
        print_correlation_statistics(output_file)

        # Automatically sort the results
        sorted_file = f"correl_{time_period}_sorted.txt"
        print(f"\nSorting correlation results...")
        try:
            import subprocess
            result = subprocess.run([
                'python', 'external_sort.py', output_file, sorted_file,
                '--chunk-size', '100', '--sort-column', '3', '--reverse'
            ], capture_output=True, text=True, cwd='.')

            if result.returncode == 0:
                print(f"Results sorted and saved to {sorted_file}")
            else:
                print(f"Sorting failed: {result.stderr}")
        except Exception as e:
            print(f"Could not run external sort: {e}")
            print("You can manually sort using:")
            print(f"python external_sort.py {output_file} {sorted_file} --chunk-size 100 --sort-column 3 --reverse")

    print("Analysis complete!")

if __name__ == "__main__":
    main()
